// menu
.menu-list {
  width: 224px;
  //menu item
  .menu-item {
    margin-left: 0;
    width: 100%;
    justify-content: flex-start;
    border-radius: 0;
    padding: 11px 16px;
    line-height: 1.8rem;
    font-weight: 600;

    &.horizontal {
      border-top: 1px solid rgba(22, 24, 35, 0.12);
    }

    &:hover {
      // background-color: red;
      background-color: rgba(22, 24, 35, 0.03);
    }
  }
}
.menu-popper {
  padding-bottom: 8px;
}

.menu-body {
  overflow-y: auto;
}
//header.js
.header {
  position: relative;
  height: 50px;
  margin-top: -8px;
  flex-shrink: 0;
}
.back-btn {
  width: 50px;
  height: 100%;
  cursor: pointer;
}
.header-title {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
