.wrapper {
  --search-border-radius: 92px;
  --search-height: 46px;
  --search-top-spacer: 9px;
  --search-btn-width: 52px;

  height: var(--default-layout-header-height);
  box-shadow: 0px 1px 1px rgb(0 0 0 /12%);
  background-color: var(--white);
  display: flex;
  z-index: 10;
  justify-content: center;
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
}
.inner {
  height: 100%;
  width: var(--default-layout-width, 1150px);
  padding: 0 var(--default-layout-horizontal-spacer);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

//logo
.logo-link {
  display: flex;
}

//header action
.actions {
  display: flex;
  align-items: center;
}
// HEADER -> ACTIONS (chưa login)
.more-btn {
  font-size: 2rem;
  margin-left: 26px;
  padding: 4px 8px;
  cursor: pointer;
}

// HEADER -> ACTIONS (đã login)
.action-btn {
  display: flex;
  align-items: center;
  font-size: 2.2rem;
  padding: 4px 10px;
  color: #161823;
  cursor: pointer;
  position: relative;
}

.badge {
  position: absolute;
  padding: 2px 7px;
  top: 1px;
  right: 4px;
  border-radius: 999px;
  font-size: 1rem;
  font-family: var(--font-family);
  background-color: var(--primary);
  color: var(--white);
}
.user-avatar {
  width: 32px;
  height: 32px;
  object-fit: cover;
  border-radius: 999px;
  margin-left: 12px;
  cursor: pointer;
}
