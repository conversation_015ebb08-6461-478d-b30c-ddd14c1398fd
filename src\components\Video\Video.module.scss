.wrapper {
  --size-data: calc(320px + 16vw);
  position: relative;
  display: flex;
  margin-top: 10px;
  padding-bottom: 40px;
  border-bottom: 1px solid #e3e3e4;

  flex-direction: column;
  align-items: center;
  justify-content: center;
}
//user
.user {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.user-info {
  display: flex;
  padding: 8px;
  align-items: center;
  cursor: pointer;
}
.avatar {
  width: 56px;
  height: 56px;
  border-radius: 999px;
  object-fit: cover;
}

.item-info {
  flex: 1;
  margin-left: 12px;
}
.nickname {
  font-size: 1.6rem;
  font-weight: 700;
  color: var(--text-color);
}
.check {
  margin-left: 4px;
  font-size: 1.4rem;
  color: #20d5ec;
}
.name {
  font-size: 1.3rem;
  font-weight: 400;
  color: #888;
}
//VIDEO
.video {
  position: relative;
  display: flex;
  margin-top: 10px;
}
.video-container {
  position: relative;
  border-radius: 8px;
  margin-right: 20px;
  overflow: hidden;
  height: var(--size-data);

  .video-content {
    width: calc(var(--size-data) / 100 * 56.25);
    cursor: pointer;
    height: 100%;
    width: 100%;
    border-radius: 8px;

    img {
      height: 100%;
      object-fit: cover;
      width: 100%;
      border-radius: 8px;
    }
    video {
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      object-fit: cover;
      width: 100%;
      border-radius: 8px;
    }
  }
}
.video-content {
}
.play-btn {
  position: absolute;
  color: var(--white);
  top: 12px;
  left: 6px;
}
.volume-container {
  color: var(--white);
}
.volume-slider {
  display: flex;
  width: 64px;
  height: 25px;
  background-color: rgba(22, 24, 35, 0.34);
  border-radius: 32px;
  position: absolute;
  top: 10px;
  right: 6px;
  // opacity: 0;
  input {
    position: absolute;
    top: 12px;
    right: 7px;
    -webkit-appearance: none;
    background-color: var(--white);
    height: 1px;
    width: 50px;

    &::-webkit-slider-thumb {
      -webkit-appearance: none;
      width: 12px;
      height: 12px;
      background-color: var(--white);
      border-radius: 999px;
      cursor: pointer;
    }
  }
}
.volume-btn {
  position: absolute;
  top: 12px;
  right: 74px;
}
.video-interaction {
}
