{"name": "tiktok", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@tippyjs/react": "^4.2.6", "axios": "^1.10.0", "classnames": "^2.5.1", "normalize.css": "^8.0.1", "prop-types": "^15.8.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.6.3", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-app-rewired eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"babel-plugin-module-resolver": "^5.0.2", "customize-cra": "^1.0.0", "react-app-rewired": "^2.2.1", "sass": "^1.89.2"}}