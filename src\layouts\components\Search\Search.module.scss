// HEADER -> SEARCH
.search,
.search-result {
  width: 361px;
}
.search-title {
  height: 30px;
  padding: 6px 12px;
  font-size: 1.4rem;
  font-weight: 600;
  color: rgba(22, 24, 35, 0.5);
}
.search {
  position: relative;
  height: var(--search-height);
  border-radius: var(--search-border-radius);
  background-color: rgba(22, 24, 35, 0.06);
  padding-left: 16px;
  display: flex;
  border: 1.5px solid transparent;

  &::after {
    content: "";
    position: absolute;
    width: 1px;
    height: calc(var(--search-height) - var(--search-top-spacer) * 2);
    background-color: rgba(22, 24, 35, 0.12);
    right: var(--search-btn-width);
    top: var(--search-top-spacer);
  }

  &:focus-within {
    border-color: rgba(22, 24, 35, 0.2);
  }
}

.search-input {
  height: 100%;
  font-size: 1.6rem;
  padding-right: 40px;
  color: var(--black);
  flex: 1;
  caret-color: var(--primary);
  font-family: var(--font-family);
}
.search-input:not(:placeholder-shown) ~ .search-btn {
  color: rgba(22, 24, 35, 0.75);
}

.clear,
.loading {
  position: absolute;
  right: calc(var(--search-btn-width) + 16px);
  top: 50%;
  transform: translateY(-50%);
  color: rgba(22, 24, 35, 0.34);
  font-size: 1.6rem;
  cursor: pointer;
}
.loading {
  animation: spinner 1s linear infinite;
}
//animation
@keyframes spinner {
  from {
    transform: translateY(-50%) rotate(0);
  }
  to {
    transform: translateY(-50%) rotate(360deg);
  }
}
.search-btn {
  width: var(--search-btn-width);
  height: 100%;
  border-top-right-radius: var(--search-border-radius);
  border-bottom-right-radius: var(--search-border-radius);
  font-size: 1.8rem;
  color: rgba(22, 24, 35, 0.34);
  &:hover {
    background-color: rgba(22, 24, 35, 0.03);
    cursor: pointer;
  }
  &:active {
    background-color: rgba(22, 24, 35, 0.06);
  }
}
